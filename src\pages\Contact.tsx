import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Phone, Mail, MapPin, Clock, Send, MessageCircle, HeadphonesIcon, ArrowRight } from "lucide-react";
import { Header } from "@/components/Header";
import { useToast } from "@/hooks/use-toast";
import { SEO } from "@/components/SEO";

const Contact = () => {
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    phone: "",
    subject: "",
    message: ""
  });
  const { toast } = useToast();

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    // Create mailto link with contact details
    const subject = `Contact Form: ${formData.subject}`;
    const body = `
Hello KlikaHelper Team,

Name: ${formData.name}
Email: ${formData.email}
Phone: ${formData.phone}
Subject: ${formData.subject}

Message:
${formData.message}

Best regards,
${formData.name}
    `.trim();

    const mailtoLink = `mailto:<EMAIL>?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`;
    window.location.href = mailtoLink;

    toast({
      title: "Email Client Opened!",
      description: "Please send the email to complete your contact request.",
    });
  };

  const contactInfo = [
    {
      icon: Phone,
      title: "Phone Support",
      details: "(*************",
      description: "Available 24/7 for urgent inquiries",
      color: "from-blue-400 to-cyan-500"
    },
    {
      icon: Mail,
      title: "Email Support",
      details: "<EMAIL>",
      description: "We respond within 2 hours",
      color: "from-cyan-400 to-teal-500"
    },
    {
      icon: MapPin,
      title: "Service Areas",
      details: "Durban",
      description: "Expanding to more cities soon",
      color: "from-teal-400 to-emerald-500"
    },
    {
      icon: Clock,
      title: "Business Hours",
      details: "24/7 Customer Support",
      description: "Always here when you need us",
      color: "from-emerald-400 to-green-500"
    }
  ];

  const faqs = [
    {
      question: "How quickly can I book a service?",
      answer: "Most services can be booked for the same day, subject to helper availability. We recommend booking at least 24 hours in advance for the best selection."
    },
    {
      question: "Are all helpers background checked?",
      answer: "Yes, every helper undergoes comprehensive background checks, identity verification, and reference checks before joining our platform."
    },
    {
      question: "What if I'm not satisfied with the service?",
      answer: "We offer a 100% satisfaction guarantee. If you're not happy with the service, we'll work to make it right or provide a full refund."
    },
    {
      question: "How do I pay for services?",
      answer: "Payment is handled directly with your helper. We accept cash, card payments, and electronic transfers for your convenience."
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-white via-blue-50/30 to-cyan-50/50">
      <SEO
        title="Contact KlikaHelper - Get Help & Support | 24/7 Customer Service"
        description="Contact KlikaHelper for support, questions, or booking assistance. Available 24/7 at (************* or <EMAIL>. Serving Johannesburg & Cape Town."
        url="https://klikahelper.co.za/contact"
        keywords="contact KlikaHelper, customer support, help desk, phone number, email support, 24/7 service, home helper support"
      />
      <Header />
      
      {/* Hero Section */}
      <section className="relative py-20 overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-white via-blue-50/20 to-cyan-50/30"></div>
        <div className="absolute inset-0 bg-[linear-gradient(rgba(59,130,246,0.03)_1px,transparent_1px),linear-gradient(90deg,rgba(59,130,246,0.03)_1px,transparent_1px)] bg-[size:4rem_4rem]"></div>
        
        <div className="container mx-auto px-4 relative z-10">
          <div className="max-w-4xl mx-auto text-center">
            <Badge className="mb-6 bg-gradient-to-r from-primary to-blue-500 text-white border-0 px-6 py-2 text-base font-semibold">
              Contact Us
            </Badge>
            <h1 className="text-5xl md:text-6xl font-black text-gray-800 mb-6">
              We're Here to
              <span className="block bg-gradient-to-r from-primary via-blue-500 to-cyan-500 bg-clip-text text-transparent">
                Help You
              </span>
            </h1>
            <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto leading-relaxed">
              Have questions about our services? Need help with a booking? Our friendly support team is available 24/7 to assist you.
            </p>
          </div>
        </div>
      </section>

      {/* Contact Info Section */}
      <section className="py-20 bg-white/50">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
              {contactInfo.map((info, index) => (
                <Card key={index} className="group relative bg-white/80 backdrop-blur-sm border-blue-100 hover:border-primary/30 rounded-3xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105">
                  <CardHeader className="text-center pb-6">
                    <div className={`w-16 h-16 bg-gradient-to-r ${info.color} rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:rotate-12 transition-transform duration-300 shadow-lg`}>
                      <info.icon className="h-8 w-8 text-white" />
                    </div>
                    <CardTitle className="text-lg font-bold text-gray-800 group-hover:text-transparent group-hover:bg-gradient-to-r group-hover:from-primary group-hover:to-blue-500 group-hover:bg-clip-text transition-all duration-300">
                      {info.title}
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="text-center">
                    <div className="text-lg font-semibold text-gray-800 mb-2">{info.details}</div>
                    <CardDescription className="text-gray-600 text-sm">
                      {info.description}
                    </CardDescription>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Contact Form Section */}
      <section className="py-20 bg-gradient-to-r from-primary/5 to-blue-500/5">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-4xl font-bold text-gray-800 mb-6">
                Send Us a Message
              </h2>
              <p className="text-xl text-gray-600">
                Fill out the form below and we'll get back to you as soon as possible.
              </p>
            </div>

            <Card className="bg-white/80 backdrop-blur-xl border-blue-100 shadow-2xl rounded-3xl overflow-hidden">
              <CardHeader className="bg-gradient-to-r from-primary/5 to-blue-500/5 border-b border-blue-100">
                <div className="flex items-center justify-center gap-3">
                  <MessageCircle className="h-6 w-6 text-primary" />
                  <CardTitle className="text-2xl text-gray-800">Contact Form</CardTitle>
                </div>
                <CardDescription className="text-gray-600 text-center">
                  We'll respond to your message within 2 hours during business hours
                </CardDescription>
              </CardHeader>
              
              <CardContent className="p-8">
                <form onSubmit={handleSubmit} className="space-y-6">
                  <div className="grid md:grid-cols-2 gap-6">
                    <div className="space-y-2">
                      <Label htmlFor="name" className="text-base font-medium text-gray-700">Full Name *</Label>
                      <Input
                        id="name"
                        value={formData.name}
                        onChange={(e) => setFormData({...formData, name: e.target.value})}
                        placeholder="Enter your full name"
                        className="h-12 text-base border-2 hover:border-primary/50 focus:border-primary transition-colors"
                        required
                      />
                    </div>
                    
                    <div className="space-y-2">
                      <Label htmlFor="email" className="text-base font-medium text-gray-700">Email Address *</Label>
                      <Input
                        id="email"
                        type="email"
                        value={formData.email}
                        onChange={(e) => setFormData({...formData, email: e.target.value})}
                        placeholder="Enter your email address"
                        className="h-12 text-base border-2 hover:border-primary/50 focus:border-primary transition-colors"
                        required
                      />
                    </div>
                  </div>
                  
                  <div className="grid md:grid-cols-2 gap-6">
                    <div className="space-y-2">
                      <Label htmlFor="phone" className="text-base font-medium text-gray-700">Phone Number</Label>
                      <Input
                        id="phone"
                        value={formData.phone}
                        onChange={(e) => setFormData({...formData, phone: e.target.value})}
                        placeholder="Enter your phone number"
                        className="h-12 text-base border-2 hover:border-primary/50 focus:border-primary transition-colors"
                      />
                    </div>
                    
                    <div className="space-y-2">
                      <Label htmlFor="subject" className="text-base font-medium text-gray-700">Subject *</Label>
                      <Input
                        id="subject"
                        value={formData.subject}
                        onChange={(e) => setFormData({...formData, subject: e.target.value})}
                        placeholder="What's this about?"
                        className="h-12 text-base border-2 hover:border-primary/50 focus:border-primary transition-colors"
                        required
                      />
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="message" className="text-base font-medium text-gray-700">Message *</Label>
                    <Textarea
                      id="message"
                      value={formData.message}
                      onChange={(e) => setFormData({...formData, message: e.target.value})}
                      placeholder="Tell us how we can help you..."
                      rows={6}
                      className="text-base border-2 hover:border-primary/50 focus:border-primary transition-colors resize-none"
                      required
                    />
                  </div>
                  
                  <Button
                    type="submit"
                    className="w-full h-14 text-lg font-bold bg-gradient-to-r from-primary to-blue-500 hover:from-primary/90 hover:to-blue-500/90 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300"
                    disabled={!formData.name || !formData.email || !formData.subject || !formData.message}
                  >
                    <Send className="mr-2 h-5 w-5" />
                    Send Message
                  </Button>
                </form>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-20 bg-white/50">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-4xl font-bold text-gray-800 mb-6">
                Frequently Asked Questions
              </h2>
              <p className="text-xl text-gray-600">
                Quick answers to common questions
              </p>
            </div>
            
            <div className="space-y-6">
              {faqs.map((faq, index) => (
                <Card key={index} className="bg-white/80 backdrop-blur-sm border-blue-100 hover:border-primary/30 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300">
                  <CardHeader>
                    <CardTitle className="text-lg font-bold text-gray-800 flex items-center gap-3">
                      <HeadphonesIcon className="h-5 w-5 text-primary" />
                      {faq.question}
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <CardDescription className="text-gray-600 leading-relaxed">
                      {faq.answer}
                    </CardDescription>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-primary via-blue-500 to-cyan-500">
        <div className="container mx-auto px-4 text-center">
          <h3 className="text-3xl md:text-4xl font-bold text-white mb-6">
            Ready to Get Started?
          </h3>
          <p className="text-lg text-blue-100 mb-8 max-w-2xl mx-auto">
            Don't wait – book your trusted home helper today and experience the KlikaHelper difference.
          </p>
          <Button
            size="lg"
            className="bg-white text-primary hover:bg-gray-50 font-bold px-12 py-4 rounded-full shadow-2xl hover:shadow-white/25 transition-all duration-300 hover:scale-105 transform"
          >
            Book a Service Now
            <ArrowRight className="ml-3 h-5 w-5" />
          </Button>
        </div>
      </section>
    </div>
  );
};

export default Contact;
